
# Civility Chat — MVP Implementation Plan

> **Goal:** Deliver a proof‑of‑concept mobile chat app with AI moderation using the Expo React Native stack defined in *tech‑stack.md*.

---

## 📅 High‑Level Roadmap
| Phase | Focus |
|-------|-------|
| **0** | Tooling & project skeleton
| **1** | Core chat flow (unauthenticated → moderated → delivered)
| **2** | Essential UX polish (auth flow, avatars, badges)
| **3** | Push notifications & basic telemetry
| **4** | Hardening (E2EE keys, minimal CI)
| **5** | Pilot test & feedback loop

---

## Phase 0 — Tooling & Skeleton

| # | Task | Success check |
|---|------|---------------|
| 0.1 | Initialise Git repo, enable GitHub Actions template. | `main` branch pushes trigger lint job. |
| 0.2 | `npx create-expo-app civility-chat` (TypeScript). | App runs on emulator ↔ reloads on save. |
| 0.3 | Add **Firebase** project; copy web config to `env.ts`. | `firebase login` & `firebase deploy --only functions` succeed (no functions yet). |
| 0.4 | Commit baseline `README`, *tech‑stack.md*, *project‑requirements.md*. | Repo pushed; docs render on GitHub. |

---

## Phase 1 — Core Chat Flow

| # | Task | Success check |
|---|------|---------------|
| 1.1 | Install deps: `expo install firebase expo-secure-store react-native-gifted-chat` | `npm tsc --noEmit` passes. |
| 1.2 | Implement **Firebase Auth (email link)** in `useAuth.ts`. | Sign‑in link logs user and persists after restart. |
| 1.3 | Create Firestore schema `chats/{threadId}/messages/{msgId}`. | Console shows realtime updates between two devices. |
| 1.4 | Build chat UI with **Gifted‑Chat**. | Messages send & display; scroll smooth at 60 fps. |
| 1.5 | Write **Cloud Function `moderateMessage`** calling Gemini Flash Lite. | Toxic sample returns `decision:"block"` in under 400 ms. |
| 1.6 | On blocked verdict, update Firestore doc `{ flagged:true }`; UI shows 🛑 placeholder. | Second device hides blocked content by default. |

---

## Phase 2 — UX Polish

| # | Task | Success check |
|---|------|---------------|
| 2.1 | Add **React Navigation**: Auth stack → Chat stack. | Unauth user forced to sign in; back navigation works. |
| 2.2 | Save user avatar & display name in `users/{uid}`. | Avatar bubbles render in chat list. |
| 2.3 | Display yellow “⚠️ Warn” banner for `decision:"warn"` messages. | Sender can tap *Send Anyway* to proceed. |

---

## Phase 3 — Notifications & Telemetry

| # | Task | Success check |
|---|------|---------------|
| 3.1 | Configure **Expo Notifications**; store device token per user. | Backgrounded device receives push for clean message. |
| 3.2 | Add **Sentry** crash reporting (`SENTRY_DSN` env). | Throw test error → event visible in Sentry dashboard. |
| 3.3 | Enable **Firebase Performance** to log p95 latency. | Console shows network traces <400 ms. |

---

## Phase 4 — Hardening & DX

| # | Task | Success check |
|---|------|---------------|
| 4.1 | Integrate **libsignal‑protocol‑js**; generate identity keys in `expo-secure-store`. | WireShark shows ciphertext; plaintext absent over wire. |
| 4.2 | Write Jest tests for `moderateMessage` function using mock Gemini responses. | `npm test` passes locally & in CI. |
| 4.3 | Terraform module for Firebase IAM + Function. | `terraform plan` shows no drift after apply. |

---

## Phase 5 — Pilot & Feedback

| # | Task | Success check |
|---|------|---------------|
| 5.1 | Build with **EAS Submit** to TestFlight & Play Internal. | Testers install via store links without sideloading. |
| 5.2 | Recruit **5 co‑parent pairs**; run 24‑hour pilot. | Collect ≥200 moderated messages; Gemini cost ≤$1. |
| 5.3 | Debrief, prioritise backlog items (media upload, observer accounts). | Post‑mortem doc filed; next sprint goals set. |

---

## Deferred (Post‑MVP) Items
- Media & document attachments
- Therapist/observer read‑only roles
- Postgres export & audit trail
- Granular notification settings
- Desktop/Web client via Expo‑Web

---

### Legend
- **Must‑have** = Phase 1–3 items  
- **Should‑have** = Phase 4  
- **Could‑have** = Deferred

*Last updated: 2025-07-04*
