# Civil Co‑Parenting Chat App — Project Requirements

*Codename: ****Civility***

---

## 1. Purpose & Problem Statement

Divorced or separated parents are often required to keep communication lines open to coordinate schedules, finances, and child‑related logistics. Existing messaging platforms lack built‑in safeguards against hostile, abusive, or manipulative language. **Civility** will provide a familiar, secure chat experience that automatically detects and manages toxic messages using a cloud‑based LLM (Gemini 2.5 Flash‑Lite).

## 2. Goals & Success Criteria

| ID  | Goal                               | Success Metric                                                                                |
| --- | ---------------------------------- | --------------------------------------------------------------------------------------------- |
| G‑1 | Reduce exposure to abusive content | ≥ 80 % of toxic messages blocked or warned before delivery in pilot cohort                    |
| G‑2 | Maintain conversational fluidity   | Avg. send‑to‑deliver latency ≤ 400 ms (p95)                                                   |
| G‑3 | Keep operating costs negligible    | ≤ US \$0.50 per 100 000 messages in moderation spend                                          |

## 3. Stakeholders

- **Product Owner:** Ben Chase
- **Target Users:** Co‑parents (primary), legal/therapy observers (secondary)
- **Engineering Team:** Android dev, backend/cloud engineer, UX designer
- **Advisors:** Family‑law attorneys, clinical psychologists

## 4. Scope

### In‑Scope (MVP)

1. **One‑to‑one chat** (no group rooms)
2. **Gemini Flash‑Lite** moderation pipeline (no on‑device fallback)
3. **Signal‑protocol E2EE** for message transport
4. Warn/block UX with override option
5. Basic account creation (email + OTP)
6. Minimal cloud backend (Cloud Run + Firestore)

### Out of Scope (MVP)

- Group chat, media attachments, voice/video
- Offline sending without moderation
- Therapist “observer” accounts (planned post‑MVP)

## 5. Assumptions & Dependencies

| #   | Assumption                                                      |
| --- | --------------------------------------------------------------- |
| A‑1 | Both parents consent to cloud‑based AI analysis of message text |
| A‑2 | Google Cloud free‑tier limits are sufficient during MVP pilot   |
| A‑3 | Gemini Flash‑Lite pricing remains ≤ \$0.40/M output tokens      |
| A‑4 | Pixel 8 (Android 15) is primary reference device                |

## 6. Functional Requirements

| ID    | Requirement                                                                        | Priority |
| ----- | ---------------------------------------------------------------------------------- | -------- |
| FR‑1  | Compose/send messages with typing indicator & read receipts                        | Must     |
| FR‑2  | Call Gemini API and receive JSON verdict `{decision, labels}`                      | Must     |
| FR‑3  | **Block** messages when `decision == "block"`; display re‑phrase banner            | Must     |
| FR‑4  | **Warn** messages when `decision == "warn"`; allow send with yellow highlight      | Must     |
| FR‑5  | **Allow** messages when `decision == "allow"` and forward to recipient             | Must     |
| FR‑6  | Collapse toxic messages for recipient; tap to reveal                               | Should   |
| FR‑7  | Store verdict + SHA‑256 hash for each message; auto‑delete plaintext after 30 days | Must     |
| FR‑8  | Biometric app unlock                                                               | Could    |
| FR‑9 | Push notifications (FCM)                                                           | Must     |

## 7. Non‑Functional Requirements

| ID    | Requirement                      | Target                                         |
| ----- | -------------------------------- | ---------------------------------------------- |
| NFR‑1 | **Latency (p95)** send→deliver   | ≤ 400 ms                                       |
| NFR‑2 | **Reliability** (backend uptime) | ≥ 99.9 %                                       |
| NFR‑3 | **Security**                     | OWASP MASVS L2; independent pentest pre‑launch |
| NFR‑4 | **Cost efficiency**              | ≤ US \$1/month during dev & test               |
| NFR‑5 | **Accessibility**                | Android WCAG 2.1 AA                            |

## 8. User Stories (Excerpt)

- **US‑01 (Sender):** *As a co‑parent, I want the app to warn me before I send a hostile message so I have a chance to re‑phrase.*
- **US‑02 (Recipient):** *As a co‑parent, I want toxic messages to be hidden by default so I can choose when to read them.*

## 9. Technical Architecture

```mermaid
graph TD
A[Android App] -->|E2EE Signal| B(Cloud Relay / Cloud Run)
B --> C{Gemini 2.5 Flash‑Lite}
C -->|JSON verdict| B
B --> D[(Firestore \n verdict+hash)]
B -->|Ciphertext| A2[Recipient App]
```

## 10. Technology Stack

| Layer      | Choice                                                                   |
| ---------- | ------------------------------------------------------------------------ |
| Mobile     | Kotlin + Jetpack Compose, Signal‑protocol‑java                           |
| Moderation | Gemini 2.5 Flash‑Lite (Vertex AI)                                        |
| Backend    | Cloud Run (Go or Node), Firestore |
| CI/CD      | GitHub Actions → Play Store Internal track                               |

## 11. Security & Compliance

- **E2EE**: Curve25519 keys stored in Android Keystore
- **Transport**: HTTPS with mTLS between Cloud Run ↔ Vertex AI via Private Service Connect
- **Retention**: Plaintext auto‑purged; hashes & verdicts retained 7 years (legal hold)
- **Regulations**: GDPR, CCPA; HIPAA‐adjacent safeguards (no PHI expected)

## 12. Cost Model (Dev/Test)

- Gemini tokens: ≈ US \$0.03/month for 200 msgs/day
- Cloud Run, Firestore, Build: free tier in dev
- Production @ 100 k msgs/month ≈ US \$0.45 (moderation only)

## 13. Milestones & Timeline

| Month  | Milestone                                                      |
| ------ | -------------------------------------------------------------- |
| **M1** | UI skeleton, E2EE, minimal backend                             |
| **M2** | Gemini integration, warn/block UX, push notifications          |
| **M3** | Security hardening, pilot launch (≤ 25 users) |
| **M4** | Observed feedback, performance tuning, Play Store beta         |

## 14. Risks & Mitigations

| Risk                            | Likelihood | Impact | Mitigation                                                                                    |
| ------------------------------- | ---------- | ------ | --------------------------------------------------------------------------------------------- |
| R-1 Gemini price change         | Low        | Med    | Monitor monthly; swap to open-source model if needed                                          |
| R-2 False-positives annoy users | Med        | High   | Adjustable thresholds, override option, logging for tuning                                    |
| R-3 Data-privacy concerns       | High       | High   | Ensure encrypted transport and clear consent for cloud-based moderation; no redaction for MVP |
