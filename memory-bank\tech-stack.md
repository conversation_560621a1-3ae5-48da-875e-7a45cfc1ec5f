
# Civility — Tech Stack (Expo React Native MVP)

## Guiding Principles
- **One language everywhere (TypeScript)** → strongest LLM support for vibe‑coding.
- **Serverless first** → no infrastructure to babysit during pilot.
- **Free‑tier friendly** → <$1 /mo until scale.
- **Swappable components** → path to audit‑ready Postgres or custom back‑end without rewrite.

## Stack Overview

| Layer | Choice | Rationale |
|-------|--------|-----------|
| **Mobile client** | **Expo React Native (TypeScript)** | Single code‑base, OTA updates, huge AI snippet corpus. |
| **UI components** | `react-native-gifted-chat`, `react-navigation`, `react-native-paper` | Pre‑built chat widgets & navigation that look native. |
| **E2EE** | **libsignal‑protocol‑js** via `@privacyresearch/libsignal-protocol-typescript` + keys in **expo‑secure‑store** | Meets Signal‑protocol E2EE requirement with RN support. |
| **Auth** | **Firebase Authentication** (Email‑link, Phone) | Instant onboarding, no custom server logic. |
| **Realtime DB** | **Cloud Firestore** (`chats/{threadId}/messages/{msgId}`) | WebSocket listeners, auto‑scale, free quota. |
| **Moderation** | **Node 20 Cloud Function** `moderateMessage` → **Gemini 2.5 Flash‑Lite** | ≤400 ms p95 latency, <$0.40 per 100k msgs. |
| **Media (post‑MVP)** | **Firebase Storage** | Same SDK; CDN auto‑served. |
| **Push** | **Expo Notifications** (wraps FCM + APNs) | 5‑minute setup, no native certs. |
| **Build & Release** | **EAS Build / Submit** | Cloud iOS/Android builds from GitHub trunk; `eas update` for hot‑patches. |
| **CI/CD** | **GitHub Actions** (`lint → test → EAS build`) | Fits Expo & Firebase CLI workflows. |
| **IaC (optional)** | **Terraform** modules for Firebase project + IAM + Function | Reproducible infra once architecture stabilizes. |
| **Monitoring** | **Firebase Performance** + **Sentry RN SDK** | Crash & perf telemetry, no extra servers. |

## Key NPM / Expo Packages

```bash
expo install firebase expo-secure-store expo-notifications
npm i react-native-gifted-chat react-navigation
npm i @privacyresearch/libsignal-protocol-typescript
```

## Moderation Cloud Function (skeleton)

```ts
import { onDocumentCreated } from 'firebase-functions/v2/firestore';
import { GoogleGenerativeAI } from '@google/generative-ai';

export const moderateMessage = onDocumentCreated(
  'chats/{threadId}/messages/{msgId}',
  async (event) => {
    const snap = event.data;
    if (!snap) return;
    const { text } = snap.data();

    const gemini = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
    const verdict = await gemini.moderateText(text); // pseudo helper

    if (verdict.decision !== 'allow') {
      await snap.ref.update({
        flagged: true,
        decision: verdict.decision,
        labels: verdict.labels,
        text: verdict.decision === 'block'
          ? '🛑 Message removed by moderation'
          : text,
      });
    }
  },
);
```

## Solo‑Dev Timeline (7‑Day Sprint)

| Day | Deliverable |
|-----|-------------|
| 1 | Expo scaffold, Firebase Auth wired |
| 2 | Firestore chat + Gifted‑Chat UI |
| 3 | Signal key exchange + encrypt/decrypt |
| 4 | Cloud Function moderation + UI badges |
| 5 | Expo push notifications |
| 6 | Polish + internal TestFlight / Play build |
| 7 | 5‑user pilot, collect feedback & Gemini cost stats |

## Migration Path

1. **Audit‑ready:** Export Firestore to Postgres/Supabase; add pgcrypto row signing.  
2. **Scale:** Replace Cloud Function with Cloud Run + Pub/Sub buffering.  
3. **Desktop/Web:** Recompile client with Expo‑Web or consider Flutter rewrite when UI polish becomes paramount.
